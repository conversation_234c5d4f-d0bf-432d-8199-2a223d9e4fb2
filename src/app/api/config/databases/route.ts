import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

// 数据库配置缓存
let databaseConfigsCache: Record<string, any> | null = null;
let configsCacheExpiry: number = 0;
const CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存



export async function GET() {
  try {
    const now = Date.now();

    // 如果缓存有效，直接返回
    if (databaseConfigsCache && now < configsCacheExpiry) {
      return NextResponse.json({
        success: true,
        data: databaseConfigsCache
      });
    }

    // 从数据库获取配置
    const configs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        description: true,
        accessLevel: true,
        defaultSort: true,
        sortOrder: true,
        exportConfig: true
      },
      orderBy: { sortOrder: 'asc' }
    });

    // 构建配置对象
    const configsMap: Record<string, any> = {};
    configs.forEach(config => {
      // 从 exportConfig 中获取图标和排序信息
      const exportConfig = config.exportConfig as any || {};
      const icon = exportConfig.icon || '📊';
      const categoryOrder = exportConfig.categoryOrder || config.sortOrder || 99;
      const orderInCategory = exportConfig.orderInCategory || 1;

      configsMap[config.code] = {
        name: config.name,
        category: config.category,
        description: config.description || '',
        accessLevel: config.accessLevel,
        defaultSort: config.defaultSort || null,
        sortOrder: config.sortOrder,
        categoryOrder: categoryOrder,
        orderInCategory: orderInCategory,
        icon: icon,
      };
    });

    // 设置缓存
    databaseConfigsCache = configsMap;
    configsCacheExpiry = now + CACHE_TTL;

    return NextResponse.json({
      success: true,
      data: configsMap
    });

  } catch (error) {
    console.error('获取数据库配置失败:', error);

    // 返回错误，不再使用硬编码回退
    return NextResponse.json({
      success: false,
      error: '无法获取数据库配置，请检查数据库连接',
      data: {}
    }, { status: 500 });
  }
}
