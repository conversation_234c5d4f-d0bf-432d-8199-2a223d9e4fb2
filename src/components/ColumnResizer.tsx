import React from 'react';
import { GripVertical } from 'lucide-react';

export interface ColumnResizerProps {
  fieldName: string;
  isResizing: boolean;
  isActive: boolean;
  onMouseDown: (fieldName: string, event: React.MouseEvent) => void;
  className?: string;
}

export const ColumnResizer: React.FC<ColumnResizerProps> = ({
  fieldName,
  isResizing,
  isActive,
  onMouseDown,
  className = ''
}) => {
  const handleMouseDown = (event: React.MouseEvent) => {
    onMouseDown(fieldName, event);
  };

  return (
    <div
      className={`
        absolute right-0 top-0 bottom-0 w-1
        cursor-col-resize
        group
        hover:bg-blue-400
        ${isActive ? 'bg-blue-500' : 'bg-transparent'}
        ${isResizing ? 'bg-blue-500' : ''}
        transition-colors duration-150
        ${className}
      `}
      onMouseDown={handleMouseDown}
      style={{
        zIndex: 15,
        marginRight: '-2px' // 让分隔符稍微向右偏移，更容易点击
      }}
    >
      {/* 可见的拖拽手柄 - 只在悬停时显示 */}
      <div
        className={`
          absolute inset-0
          flex items-center justify-center
          opacity-0 group-hover:opacity-100
          transition-opacity duration-150
          bg-blue-500
          ${isActive || isResizing ? 'opacity-100' : ''}
        `}
      >
        <GripVertical
          className="h-4 w-4 text-white"
          style={{
            transform: 'scale(0.8)',
            filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))'
          }}
        />
      </div>

      {/* 扩展的点击区域 */}
      <div
        className="absolute inset-y-0 -left-2 -right-2 cursor-col-resize"
        style={{ zIndex: 16 }}
        title="拖拽调整列宽"
      />
    </div>
  );
};

export default ColumnResizer;
