import type { DatabaseConfig } from '@/lib/configCache';

// 类型守卫：验证日期字符串
const isValidDate = (dateString: string | null): dateString is string => {
  if (!dateString) return false;
  const date = new Date(dateString);
  return !Number.isNaN(date.getTime());
};

/**
 * 根据searchParams和config动态构建Prisma where对象
 * 重构版本：移除对database字段的依赖
 */
export function buildMedicalDeviceWhere(searchParams: URLSearchParams, config: DatabaseConfig): Record<string, unknown> {
  // 通用筛选参数 - 移除database字段处理
  const filters: Record<string, unknown> = {};

  // 获取所有参数，包括多值参数
  const processedKeys = new Set<string>();
  searchParams.forEach((value, key) => {
    // 移除database字段处理，简化过滤逻辑
    if (!['page', 'limit', 'sortBy', 'sortOrder', 'allFields'].includes(key) && !processedKeys.has(key)) {
      const allValues = searchParams.getAll(key);
      filters[key] = allValues.length === 1 ? allValues[0] : allValues;
      processedKeys.add(key);
    }
  });

  const where: Record<string, unknown> = {};

  // Keyword search across multiple searchable fields
  const globalKeyword = searchParams.get('allFields');

  for (const key in filters) {
    const value = filters[key];
    if (value === null || value === '') continue;
    
    const fieldConfig = config.fields.find(f => f.fieldName === key);
    if (fieldConfig) {
      if (fieldConfig.fieldType === 'date') {
        if (key.endsWith('From') && typeof value === 'string' && isValidDate(value)) {
          const field = key.replace('From', '');
          where[field] = { ...(where[field] as object || {}), gte: new Date(value) };
        } else if (key.endsWith('To') && typeof value === 'string' && isValidDate(value)) {
          const field = key.replace('To', '');
          where[field] = { ...(where[field] as object || {}), lte: new Date(value) };
        } else if (isValidDate(value as string)) {
          where[key] = new Date(value as string);
        }
      } else if (fieldConfig.searchType === 'contains' && typeof value === 'string') {
        where[key] = { contains: value, mode: 'insensitive' };
      } else if (fieldConfig.fieldType === 'boolean') {
        where[key] = value === 'true';
      } else {
        // 处理多选和单选字段
        if (Array.isArray(value)) {
          // 多选字段处理
          const hasNullValue = value.includes('N/A');
          if (hasNullValue) {
            const nonNullValues = value.filter(v => v !== 'N/A');
            if (nonNullValues.length > 0) {
              // 既有具体值又有N/A，使用OR条件
              where.OR = [
                { [key]: { in: nonNullValues } },
                { [key]: null },
                { [key]: '' }
              ];
            } else {
              // 只选择了N/A
              where.OR = [
                { [key]: null },
                { [key]: '' }
              ];
            }
          } else {
            where[key] = { in: value };
          }
        } else if (value === 'N/A') {
          // 单选字段选择了N/A
          where.OR = [
            { [key]: null },
            { [key]: '' }
          ];
        } else {
          where[key] = value;
        }
      }
    } else {
      // 未配置字段，回退原逻辑
      if (key.endsWith('From') && typeof value === 'string' && isValidDate(value)) {
        const field = key.replace('From', '');
        where[field] = { ...(where[field] as object || {}), gte: new Date(value) };
      } else if (key.endsWith('To') && typeof value === 'string' && isValidDate(value)) {
        const field = key.replace('To', '');
        where[field] = { ...(where[field] as object || {}), lte: new Date(value) };
      } else if (key.startsWith('is') && (value === 'true' || value === 'false')) {
        where[key] = value === 'true';
      } else if (typeof value === 'string' && ['productName', 'companyName', 'registrationNumber', 'structureOrUse'].includes(key)) {
        where[key] = { contains: value, mode: 'insensitive' };
      } else {
        where[key] = value;
      }
    }
  }

  // If allFields keyword provided, build OR conditions across isSearchable fields with contains
  if (globalKeyword && globalKeyword.trim()) {
    const keyword = globalKeyword.trim();
    const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');
    if (searchableFields.length > 0) {
      // 检查是否已有OR条件（如N/A筛选）
      if (where.OR) {
        // 已有OR条件，需要用AND组合
        const existingConditions = { ...where };
        where = {
          AND: [
            existingConditions,
            {
              OR: searchableFields.map(f => ({ [f.fieldName]: { contains: keyword, mode: 'insensitive' } }))
            }
          ]
        };
      } else if (Object.keys(where).length > 0) {
        // 有其他条件但没有OR，用AND组合
        const existingConditions = { ...where };
        where = {
          AND: [
            existingConditions,
            {
              OR: searchableFields.map(f => ({ [f.fieldName]: { contains: keyword, mode: 'insensitive' } }))
            }
          ]
        };
      } else {
        // 没有其他条件，直接使用全局搜索
        where.OR = searchableFields.map(f => ({ [f.fieldName]: { contains: keyword, mode: 'insensitive' } }));
      }
    }
  }

  return where;
} 