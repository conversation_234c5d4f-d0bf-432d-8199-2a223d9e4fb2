/* eslint-disable @typescript-eslint/no-explicit-any */
import { getSession } from './session';
import type { User } from './auth';

// Database access level cache
let databaseAccessCache: Map<string, string> | null = null;
let cacheExpiry: number = 0;
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes cache

// Get access level configuration from API
async function getDatabaseAccessLevels(): Promise<Map<string, string>> {
  const now = Date.now();

  // If cache is valid, return directly
  if (databaseAccessCache && now < cacheExpiry) {
    return databaseAccessCache;
  }

  try {
    // Get complete configuration
    const configs = await getDatabaseConfigs();

    // Build access level cache
    databaseAccessCache = new Map();
    Object.entries(configs).forEach(([code, config]: [string, any]) => {
      databaseAccessCache!.set(code, config.accessLevel);
    });

    // Set cache expiry time
    cacheExpiry = now + CACHE_TTL;

    return databaseAccessCache;
  } catch (error) {
    console.warn('Failed to get database access level configuration, using default config:', error);

    // Fallback to hardcoded configuration
    const fallbackConfig = new Map([
      ['deviceCNImported', 'free'],
      ['us_pmn', 'premium'],
    ]);

    databaseAccessCache = fallbackConfig;
    cacheExpiry = now + CACHE_TTL;

    return fallbackConfig;
  }
}

// Membership benefits configuration
export const MEMBERSHIP_BENEFITS = {
  free: {
    name: 'Free User',
    description: 'Basic data access',
    features: [
      'Pharmaceutical patent data query',
      'China mainland evaluation progress',
      'Basic filtering functions',
      'Daily query limit: 50 times'
    ],
    accessDatabases: ['deviceCNImported'],
    queryLimit: 50,
    exportLimit: 0,
  },

  premium: {
    name: 'Premium Member',
    description: 'Global medical device data',
    features: [
      'All free features',
      'Global medical device market data',
      'Advanced filtering and sorting',
      'Data export functionality',
      'Unlimited query access',
      'Email support'
    ],
    accessDatabases: [
      'deviceCNImported', 'us_pmn'
    ],
    queryLimit: -1, // Unlimited
    exportLimit: 1000,
    price: {
      monthly: 299,
      yearly: 2999,
    }
  },

  enterprise: {
    name: 'Enterprise',
    description: 'Complete commercial data service',
    features: [
      'All premium features',
      'License out transaction data',
      'National procurement results',
      'API interface access',
      'Bulk data export',
      'Dedicated account manager',
      'Customized reports'
    ],
    accessDatabases: [
      'deviceCNImported', 'us_pmn'
    ],
    queryLimit: -1,
    exportLimit: -1,
    price: {
      monthly: 1999,
      yearly: 19999,
    }
  }
} as const;

// User types
export type MembershipType = keyof typeof MEMBERSHIP_BENEFITS;

// Permission check function (client-side security)
export async function canAccessDatabase(userMembershipType: MembershipType | null, databaseCode: string): Promise<boolean> {
  const accessLevels = await getDatabaseAccessLevels();
  const requiredLevel = accessLevels.get(databaseCode) || 'premium';

  // Unregistered users can only access free databases
  if (!userMembershipType) {
    return requiredLevel === 'free';
  }

  const benefits = MEMBERSHIP_BENEFITS[userMembershipType];

  // Check if user level meets requirements
  switch (requiredLevel) {
    case 'free':
      return true; // All users can access free databases
    case 'premium':
      return userMembershipType === 'premium' || userMembershipType === 'enterprise';
    case 'enterprise':
      return userMembershipType === 'enterprise';
    default:
      return false;
  }
}

// Get required permission level for database (client-side security)
export async function getDatabaseAccessLevel(databaseCode: string): Promise<MembershipType> {
  const accessLevels = await getDatabaseAccessLevels();
  const level = accessLevels.get(databaseCode) || 'premium';
  return level as MembershipType;
}

// Synchronous version of permission check function (for client components)
export function canAccessDatabaseSync(userMembershipType: MembershipType | null, databaseCode: string, accessLevel?: string): boolean {
  const requiredLevel = accessLevel || 'premium';

  // Unregistered users can only access free databases
  if (!userMembershipType) {
    return requiredLevel === 'free';
  }

  // Check if user level meets requirements
  switch (requiredLevel) {
    case 'free':
      return true; // All users can access free databases
    case 'premium':
      return userMembershipType === 'premium' || userMembershipType === 'enterprise';
    case 'enterprise':
      return userMembershipType === 'enterprise';
    default:
      return false;
  }
}

// Get access restricted message
export async function getAccessRestrictedMessage(databaseCode: string, userMembershipType: MembershipType | null) {
  const requiredLevel = await getDatabaseAccessLevel(databaseCode);
  const requiredBenefits = MEMBERSHIP_BENEFITS[requiredLevel];

  if (!userMembershipType) {
    return {
      title: 'Login Required',
      message: `This database requires ${requiredBenefits.name} privileges to access.`,
      action: 'login',
      requiredLevel,
    };
  }

  return {
    title: 'Insufficient Permissions',
    message: `This database requires ${requiredBenefits.name} privileges to access.`,
    action: 'upgrade',
    requiredLevel,
  };
}

// Database display configuration cache
let databaseConfigsCache: Record<string, any> | null = null;
let configsCacheExpiry: number = 0;

// Preload configuration Promise to avoid duplicate requests
let configLoadingPromise: Promise<Record<string, any>> | null = null;

// Client-side secure database configuration getter function
export async function getDatabaseConfigs(): Promise<Record<string, any>> {
  // Check if in browser environment
  if (typeof window !== 'undefined') {
    // On client side, use API to get configuration
    return getDatabaseConfigsFromAPI();
  }

  // On server side, use database directly
  return getDatabaseConfigsFromDB();
}

// Get configuration from API (client-side)
async function getDatabaseConfigsFromAPI(): Promise<Record<string, any>> {
  const now = Date.now();

  // If cache is valid, return directly
  if (databaseConfigsCache && now < configsCacheExpiry) {
    return databaseConfigsCache;
  }

  // If there's an ongoing request, wait for it to complete
  if (configLoadingPromise) {
    return configLoadingPromise;
  }

  // Create new loading Promise
  configLoadingPromise = (async () => {
    try {
      // Get configuration from API
      const response = await fetch('/api/config/databases');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch database configs');
      }

      // Set cache
      databaseConfigsCache = result.data;
      configsCacheExpiry = now + CACHE_TTL;

      return result.data;
    } catch (error) {
      console.error('Failed to get database configuration:', error);
      // 不再使用回退配置，返回空对象
      return {};
    } finally {
      // Clear loading Promise
      configLoadingPromise = null;
    }
  })();

  return configLoadingPromise;
}

// Preload configuration function, can be called at application startup
export function preloadDatabaseConfigs(): void {
  // Async preload, doesn't block main flow
  getDatabaseConfigs().catch(error => {
    console.warn('Failed to preload database configuration:', error);
  });
}

// Get database icon from config
function getDatabaseIcon(config: any): string {
  if (config.exportConfig && config.exportConfig.icon) {
    return config.exportConfig.icon;
  }
  return '📊'; // 默认图标
}
// Backward compatible DATABASE_CONFIGS export
export const DATABASE_CONFIGS = databaseConfigsCache || {};

export type DatabaseCode = string;

// Server-side database configuration getter function
async function getDatabaseConfigsFromDB(): Promise<Record<string, any>> {
  const now = Date.now();

  // If cache is valid, return directly
  if (databaseConfigsCache && now < configsCacheExpiry) {
    return databaseConfigsCache;
  }

  try {
    // Dynamic import Prisma client (server-side only)
    const { db } = await import('./prisma');

    // Get configuration from database
    const configs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        description: true,
        accessLevel: true,
        exportConfig: true,
        sortOrder: true
      },
      orderBy: { sortOrder: 'asc' }
    });

    // Build configuration object
    const configsMap: Record<string, any> = {};
    configs.forEach(config => {
      configsMap[config.code] = {
        name: config.name,
        category: config.category,
        description: config.description || '',
        accessLevel: config.accessLevel,
        sortOrder: config.sortOrder,
        icon: getDatabaseIcon(config),
      };
    });

    // Set cache
    databaseConfigsCache = configsMap;
    configsCacheExpiry = now + CACHE_TTL;

    return configsMap;
  } catch (error) {
    console.error('Failed to get database configuration:', error);
    // 不再使用回退配置，返回空对象
    return {};
  }
}
